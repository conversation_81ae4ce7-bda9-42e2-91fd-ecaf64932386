{% extends 'base.html' %}

{% block title %}Messages | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .messaging-container {
        display: grid;
        grid-template-columns: 350px 1fr;
        height: calc(100vh - 200px);
        min-height: 600px;
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    @media (max-width: 992px) {
        .messaging-container {
            grid-template-columns: 1fr;
        }
    }

    .conversation-list {
        border-right: 1px solid var(--gray-200);
        overflow-y: auto;
    }

    .conversation-list-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .conversation-list-title {
        font-size: var(--font-lg);
        margin: 0;
    }

    .new-message-button {
        background: none;
        border: none;
        color: var(--primary);
        cursor: pointer;
        font-size: var(--font-xl);
    }

    .search-conversations {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .conversation-items {
        padding: var(--spacing-base) 0;
    }

    .conversation-item {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-xl);
        cursor: pointer;
        transition: var(--transition-base);
    }

    .conversation-item:hover {
        background-color: var(--gray-100);
    }

    .conversation-item.active {
        background-color: var(--primary);
        color: var(--white);
    }

    .conversation-item.active .conversation-name,
    .conversation-item.active .conversation-preview,
    .conversation-item.active .conversation-time {
        color: var(--white);
    }

    .conversation-item.active:hover {
        background-color: var(--primary-dark);
    }

    .conversation-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .conversation-info {
        flex: 1;
        min-width: 0;
    }

    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }

    .conversation-name {
        font-weight: var(--fw-medium);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        white-space: nowrap;
    }

    .conversation-preview {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-status {
        position: relative;
    }

    .unread-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: var(--primary);
        color: var(--white);
        font-size: 10px;
        width: 18px;
        height: 18px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .empty-conversation {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: var(--spacing-xl);
        text-align: center;
        color: var(--text-light);
    }

    .empty-conversation-icon {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: var(--spacing-xl);
    }

    .empty-conversation-message {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
    }

    .new-message-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: var(--z-50);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-base);
    }

    .new-message-modal.show {
        opacity: 1;
        visibility: visible;
    }

    .modal-content {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        width: 100%;
        max-width: 500px;
        box-shadow: var(--shadow-xl);
    }

    .modal-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .modal-title {
        font-size: var(--font-lg);
        margin: 0;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: var(--font-xl);
        cursor: pointer;
        color: var(--text-light);
    }

    .modal-body {
        padding: var(--spacing-xl);
    }

    .modal-footer {
        padding: var(--spacing-base) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
        display: flex;
        justify-content: flex-end;
        gap: var(--gap-base);
    }

    /* Conversation Detail Styles */
    .conversation-detail {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .conversation-detail-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        background-color: var(--white);
        min-height: 80px;
    }

    .conversation-detail-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
        margin-right: var(--spacing-base);
    }

    .conversation-detail-info {
        flex: 1;
    }

    .conversation-detail-name {
        font-size: var(--font-lg);
        font-weight: var(--fw-medium);
        margin-bottom: var(--spacing-xs);
    }

    .conversation-detail-status {
        font-size: var(--font-sm);
        color: var(--text-light);
    }

    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: var(--spacing-base);
        background-color: var(--gray-50);
    }

    .message {
        margin-bottom: var(--spacing-base);
        display: flex;
        align-items: flex-start;
    }

    .message.sent {
        justify-content: flex-end;
    }

    .message.received {
        justify-content: flex-start;
    }

    .message-bubble {
        max-width: 70%;
        padding: var(--spacing-sm) var(--spacing-base);
        border-radius: var(--radius-lg);
        word-wrap: break-word;
    }

    .message.sent .message-bubble {
        background-color: var(--primary);
        color: var(--white);
        border-bottom-right-radius: var(--radius-sm);
    }

    .message.received .message-bubble {
        background-color: var(--white);
        color: var(--text);
        border-bottom-left-radius: var(--radius-sm);
        box-shadow: var(--shadow-sm);
    }

    .message-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        margin-top: var(--spacing-xs);
        text-align: right;
    }

    .message.received .message-time {
        text-align: left;
    }

    .message-input-container {
        padding: var(--spacing-base) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
        background-color: var(--white);
    }

    .message-form {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .message-input {
        flex: 1;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-base);
        resize: none;
        min-height: 40px;
        max-height: 120px;
    }

    .message-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
    }

    .send-button {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        background-color: var(--primary);
        color: var(--white);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-base);
    }

    .send-button:hover {
        background-color: var(--primary-dark);
        transform: scale(1.05);
    }

    .send-button:disabled {
        background-color: var(--gray-300);
        cursor: not-allowed;
        transform: none;
    }

    /* Pet Inquiry Styles */
    .pet-inquiry-card {
        background-color: var(--primary);
        color: var(--white);
        border-radius: var(--radius-lg);
        padding: var(--spacing-base);
        margin-bottom: var(--spacing-base);
        max-width: 70%;
    }

    .pet-inquiry-header {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-sm);
        gap: var(--spacing-xs);
    }

    .pet-inquiry-price {
        background-color: rgba(255, 255, 255, 0.2);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-base);
        font-weight: var(--fw-medium);
        margin-left: auto;
    }

    .pet-inquiry-actions {
        display: flex;
        gap: var(--spacing-sm);
        margin-top: var(--spacing-base);
    }

    .inquiry-btn {
        flex: 1;
        padding: var(--spacing-sm);
        border: none;
        border-radius: var(--radius-base);
        font-weight: var(--fw-medium);
        cursor: pointer;
        transition: var(--transition-base);
        font-size: var(--font-sm);
    }

    .inquiry-btn.accept {
        background-color: var(--success);
        color: var(--white);
    }

    .inquiry-btn.decline {
        background-color: var(--danger);
        color: var(--white);
    }

    .inquiry-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-base);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="messaging-container">
        <div class="conversation-list">
            <div class="conversation-list-header">
                <h2 class="conversation-list-title">Messages</h2>
                <button type="button" class="new-message-button" id="new-message-btn">
                    <i class="fas fa-edit"></i>
                </button>
            </div>

            <div class="search-conversations">
                <input type="text" placeholder="Search conversations..." class="search-input" id="search-conversations">
            </div>

            <div class="conversation-items" id="conversation-items">
                {% for conversation in conversations %}
                    <div class="conversation-item" data-conversation-id="{{ conversation.id }}">
                        {% if conversation.other_user %}
                            {% if conversation.other_user.profile_picture %}
                                <img src="{{ conversation.other_user.profile_picture.url }}" alt="{{ conversation.other_user.username }}" class="conversation-avatar">
                            {% else %}
                                <img src="/static/img/default-avatar.svg" alt="{{ conversation.other_user.username }}" class="conversation-avatar">
                            {% endif %}
                        {% else %}
                            <img src="/static/img/default-avatar.svg" alt="User" class="conversation-avatar">
                        {% endif %}

                        <div class="conversation-info">
                            <div class="conversation-header">
                                <div class="conversation-name">
                                    {% if conversation.other_user %}
                                        {{ conversation.other_user.username }}
                                    {% else %}
                                        Unknown User
                                    {% endif %}
                                </div>
                                <div class="conversation-time">
                                    {% if conversation.last_message %}
                                        {{ conversation.last_message.created_at|date:"g:i A" }}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="conversation-preview">
                                {% if conversation.last_message %}
                                    {% if conversation.last_message.sender == user %}
                                        <span>You:</span>
                                    {% endif %}
                                    {{ conversation.last_message.content|truncatechars:30 }}
                                {% else %}
                                    No messages yet
                                {% endif %}
                            </div>
                        </div>

                        <div class="conversation-status">
                            {% if conversation.unread_count > 0 and conversation.last_message and conversation.last_message.sender != user %}
                                <div class="unread-badge">{{ conversation.unread_count }}</div>
                            {% endif %}
                        </div>
                    </div>
                {% empty %}
                    <div class="empty-state">
                        <p>No conversations yet.</p>
                        <button type="button" class="btn btn-primary" id="empty-new-message-btn">Start a Conversation</button>
                    </div>
                {% endfor %}
            </div>
        </div>

        <div class="conversation-content" id="conversation-content">
            <div class="empty-conversation" id="empty-conversation">
                <div class="empty-conversation-icon">
                    <i class="far fa-comments"></i>
                </div>
                <h3 class="empty-conversation-message">Select a conversation to start messaging</h3>
                <p>Or start a new conversation by clicking the new message button.</p>
            </div>

            <!-- Conversation Detail Container (hidden by default) -->
            <div class="conversation-detail" id="conversation-detail" style="display: none;">
                <div class="conversation-detail-header" id="conversation-header">
                    <!-- Header content will be loaded via AJAX -->
                </div>

                <div class="messages-container" id="messages-container">
                    <!-- Messages will be loaded via AJAX -->
                </div>

                <div class="message-input-container" id="message-input-container">
                    <!-- Message input will be loaded via AJAX -->
                </div>
            </div>
        </div>
    </div>

    <!-- New Message Modal -->
    <div class="new-message-modal" id="new-message-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">New Message</h3>
                <button type="button" class="modal-close" id="modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <form id="new-conversation-form">
                    <div class="form-group">
                        <label for="recipient" class="form-label">To:</label>
                        <input type="text" id="recipient" name="recipient" class="form-control" placeholder="Enter username" required>
                        <div id="recipient-suggestions" class="dropdown-menu"></div>
                    </div>

                    <div class="form-group">
                        <label for="message" class="form-label">Message:</label>
                        <textarea id="message" name="message" rows="4" class="form-control" placeholder="Type your message here..." required></textarea>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="modal-cancel-btn">Cancel</button>
                <button type="button" class="btn btn-primary" id="send-message-btn">Send Message</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let currentConversationId = null;
        let messagePollingInterval = null;

        // Initialize conversation click handlers
        initializeConversationHandlers();

        // New Message Modal
        const newMessageBtn = document.getElementById('new-message-btn');
        const emptyNewMessageBtn = document.getElementById('empty-new-message-btn');
        const newMessageModal = document.getElementById('new-message-modal');
        const modalCloseBtn = document.getElementById('modal-close-btn');
        const modalCancelBtn = document.getElementById('modal-cancel-btn');
        const sendMessageBtn = document.getElementById('send-message-btn');

        function openModal() {
            newMessageModal.classList.add('show');
        }

        function closeModal() {
            newMessageModal.classList.remove('show');
            document.getElementById('new-conversation-form').reset();
        }

        if (newMessageBtn) {
            newMessageBtn.addEventListener('click', openModal);
        }

        if (emptyNewMessageBtn) {
            emptyNewMessageBtn.addEventListener('click', openModal);
        }

        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', closeModal);
        }

        if (modalCancelBtn) {
            modalCancelBtn.addEventListener('click', closeModal);
        }

        // Close modal when clicking outside
        newMessageModal.addEventListener('click', function(e) {
            if (e.target === newMessageModal) {
                closeModal();
            }
        });

        // Send new message
        if (sendMessageBtn) {
            sendMessageBtn.addEventListener('click', function() {
                const recipient = document.getElementById('recipient').value;
                const message = document.getElementById('message').value;

                if (!recipient || !message) {
                    return;
                }

                // Send message via AJAX
                fetch(`{% url 'start-conversation' username='placeholder' %}`.replace('placeholder', recipient), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect to the new conversation
                        window.location.href = data.redirect_url;
                    } else {
                        // Show error message
                        console.error('Error:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }

        // Initialize conversation click handlers
        function initializeConversationHandlers() {
            const conversationItems = document.querySelectorAll('.conversation-item');
            conversationItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const conversationId = this.getAttribute('data-conversation-id');
                    if (conversationId) {
                        loadConversation(conversationId);
                    }
                });
            });
        }

        // Search conversations
        const searchInput = document.getElementById('search-conversations');
        const conversationItems = document.querySelectorAll('.conversation-item');

        if (searchInput && conversationItems.length) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                conversationItems.forEach(item => {
                    const name = item.querySelector('.conversation-name').textContent.toLowerCase();
                    const preview = item.querySelector('.conversation-preview').textContent.toLowerCase();

                    if (name.includes(searchTerm) || preview.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }

        // Load conversation
        function loadConversation(conversationId) {
            // Clear any existing polling
            if (messagePollingInterval) {
                clearInterval(messagePollingInterval);
            }

            // Update active conversation
            currentConversationId = conversationId;

            // Update UI to show active conversation
            updateActiveConversation(conversationId);

            // Show conversation detail and hide empty state
            document.getElementById('empty-conversation').style.display = 'none';
            document.getElementById('conversation-detail').style.display = 'flex';

            // Load conversation data
            Promise.all([
                loadConversationHeader(conversationId),
                loadMessages(conversationId),
                loadMessageInput(conversationId)
            ]).then(() => {
                // Scroll to bottom of messages
                scrollToBottom();

                // Setup message form
                setupMessageForm();

                // Start polling for new messages
                startMessagePolling();
            }).catch(error => {
                console.error('Error loading conversation:', error);
                showErrorMessage('Failed to load conversation');
            });
        }

        // Load conversation header
        function loadConversationHeader(conversationId) {
            return fetch(`/messaging/conversation-header/${conversationId}/`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('conversation-header').innerHTML = `
                        <img src="${data.user.profile_picture || '/static/img/default-avatar.svg'}"
                             alt="${data.user.username}" class="conversation-detail-avatar">
                        <div class="conversation-detail-info">
                            <div class="conversation-detail-name">${data.user.username}</div>
                            <div class="conversation-detail-status">
                                ${data.user.is_online ? '<span class="text-success">Online</span>' :
                                  `Last seen ${data.user.last_seen} ago`}
                            </div>
                        </div>
                    `;
                }
            });
        }

        // Load messages
        function loadMessages(conversationId) {
            return fetch(`/messaging/conversation-messages/${conversationId}/`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const messagesContainer = document.getElementById('messages-container');
                    messagesContainer.innerHTML = '';

                    data.messages.forEach(message => {
                        appendMessage(message);
                    });
                }
            });
        }

        // Load message input
        function loadMessageInput(conversationId) {
            return new Promise((resolve) => {
                document.getElementById('message-input-container').innerHTML = `
                    <form class="message-form" id="message-form" action="/messaging/conversation/${conversationId}/" method="post">
                        <input type="hidden" name="csrfmiddlewaretoken" value="${getCookie('csrftoken')}">
                        <textarea class="message-input" id="message-input" name="content"
                                  placeholder="Type a message..." rows="1"></textarea>
                        <button type="submit" class="send-button" id="send-button">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                `;
                resolve();
            });
        }

        // Setup message form
        function setupMessageForm() {
            const messageForm = document.getElementById('message-form');
            const messageInput = document.getElementById('message-input');

            if (messageForm) {
                messageForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const content = messageInput.value.trim();
                    if (!content) return;

                    const formData = new FormData(this);

                    fetch(this.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Add new message to the conversation
                            appendMessage(data.message);

                            // Clear input
                            messageInput.value = '';

                            // Scroll to bottom
                            scrollToBottom();

                            // Update conversation list
                            updateConversationPreview(currentConversationId, data.message);
                        } else {
                            showErrorMessage('Failed to send message');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showErrorMessage('Failed to send message');
                    });
                });

                // Auto-resize textarea
                if (messageInput) {
                    messageInput.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                    });

                    // Send on Enter (but not Shift+Enter)
                    messageInput.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            messageForm.dispatchEvent(new Event('submit'));
                        }
                    });
                }
            }
        }

        // Append message to conversation
        function appendMessage(message) {
            const messagesContainer = document.getElementById('messages-container');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${message.is_self ? 'sent' : 'received'}`;

            let messageContent = '';
            if (message.message_type === 'pet_inquiry') {
                messageContent = createPetInquiryMessage(message);
            } else {
                messageContent = `
                    <div class="message-bubble">
                        ${message.content}
                        <div class="message-time">${message.created_at}</div>
                    </div>
                `;
            }

            messageElement.innerHTML = messageContent;
            messagesContainer.appendChild(messageElement);
        }

        // Create pet inquiry message
        function createPetInquiryMessage(message) {
            return `
                <div class="pet-inquiry-card">
                    <div class="pet-inquiry-header">
                        <i class="fas fa-paw"></i>
                        <span>Pet Inquiry</span>
                        ${message.pet_price ? `<div class="pet-inquiry-price">$${message.pet_price}</div>` : ''}
                    </div>
                    <div class="message-content">${message.content}</div>
                    ${message.show_actions ? `
                        <div class="pet-inquiry-actions">
                            <button class="inquiry-btn accept" onclick="respondToInquiry(${message.id}, 'interested')">
                                <i class="fas fa-check"></i> Interested
                            </button>
                            <button class="inquiry-btn decline" onclick="respondToInquiry(${message.id}, 'declined')">
                                <i class="fas fa-times"></i> Decline
                            </button>
                        </div>
                    ` : ''}
                    <div class="message-time">${message.created_at}</div>
                </div>
            `;
        }

        // Update active conversation in sidebar
        function updateActiveConversation(conversationId) {
            const conversationItems = document.querySelectorAll('.conversation-item');
            conversationItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('data-conversation-id') === conversationId) {
                    item.classList.add('active');
                }
            });
        }

        // Update conversation preview in sidebar
        function updateConversationPreview(conversationId, message) {
            const conversationItem = document.querySelector(`[data-conversation-id="${conversationId}"]`);
            if (conversationItem) {
                const previewElement = conversationItem.querySelector('.conversation-preview');
                const timeElement = conversationItem.querySelector('.conversation-time');

                if (previewElement) {
                    previewElement.innerHTML = `<span>You:</span> ${message.content.substring(0, 30)}${message.content.length > 30 ? '...' : ''}`;
                }

                if (timeElement) {
                    timeElement.textContent = 'now';
                }

                // Move conversation to top
                const conversationList = document.getElementById('conversation-items');
                conversationList.insertBefore(conversationItem, conversationList.firstChild);
            }
        }

        // Scroll to bottom of messages
        function scrollToBottom() {
            const messagesContainer = document.getElementById('messages-container');
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        // Start polling for new messages
        function startMessagePolling() {
            if (messagePollingInterval) {
                clearInterval(messagePollingInterval);
            }

            messagePollingInterval = setInterval(() => {
                if (currentConversationId) {
                    checkForNewMessages();
                }
            }, 3000); // Poll every 3 seconds
        }

        // Check for new messages
        function checkForNewMessages() {
            const lastMessageElement = document.querySelector('#messages-container .message:last-child');
            const lastMessageId = lastMessageElement ? lastMessageElement.getAttribute('data-message-id') : 0;

            fetch(`/messaging/conversation-messages/${currentConversationId}/?last_id=${lastMessageId}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.messages.length > 0) {
                    data.messages.forEach(message => {
                        appendMessage(message);
                    });
                    scrollToBottom();
                }
            })
            .catch(error => {
                console.error('Error checking for new messages:', error);
            });
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
