{% extends 'base.html' %}

{% block title %}Messages | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .messaging-container {
        display: flex;
        height: calc(100vh - 200px);
        min-height: 600px;
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    @media (max-width: 992px) {
        .messaging-container {
            flex-direction: column;
        }
    }

    .conversation-list {
        width: 350px;
        min-width: 350px;
        border-right: 1px solid var(--gray-200);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }

    .conversation-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .conversation-list-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .conversation-list-title {
        font-size: var(--font-lg);
        margin: 0;
    }

    .new-message-button {
        background: none;
        border: none;
        color: var(--primary);
        cursor: pointer;
        font-size: var(--font-xl);
    }

    .search-conversations {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .conversation-items {
        padding: var(--spacing-base) 0;
    }

    .conversation-item {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-xl);
        cursor: pointer;
        transition: var(--transition-base);
    }

    .conversation-item:hover {
        background-color: var(--gray-100);
    }

    .conversation-item.active {
        background-color: var(--primary);
        color: var(--white);
    }

    .conversation-item.active .conversation-name,
    .conversation-item.active .conversation-preview,
    .conversation-item.active .conversation-time {
        color: var(--white);
    }

    .conversation-item.active:hover {
        background-color: var(--primary-dark);
    }

    .conversation-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .conversation-info {
        flex: 1;
        min-width: 0;
    }

    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }

    .conversation-name {
        font-weight: var(--fw-medium);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        white-space: nowrap;
    }

    .conversation-preview {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-status {
        position: relative;
    }

    .unread-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: var(--primary);
        color: var(--white);
        font-size: 10px;
        width: 18px;
        height: 18px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .empty-conversation {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: var(--spacing-xl);
        text-align: center;
        color: var(--text-light);
    }

    .empty-conversation-icon {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: var(--spacing-xl);
    }

    .empty-conversation-message {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
    }

    .new-message-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: var(--z-50);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-base);
    }

    .new-message-modal.show {
        opacity: 1;
        visibility: visible;
    }

    .modal-content {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        width: 100%;
        max-width: 500px;
        box-shadow: var(--shadow-xl);
    }

    .modal-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .modal-title {
        font-size: var(--font-lg);
        margin: 0;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: var(--font-xl);
        cursor: pointer;
        color: var(--text-light);
    }

    .modal-body {
        padding: var(--spacing-xl);
    }

    .modal-footer {
        padding: var(--spacing-base) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
        display: flex;
        justify-content: flex-end;
        gap: var(--gap-base);
    }

    /* Conversation Detail Styles */
    .conversation-detail {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .conversation-detail-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        background-color: var(--white);
        min-height: 80px;
    }

    .conversation-detail-header {
        padding: 16px 20px;
        border-bottom: 1px solid #E5E5EA;
        background-color: var(--white);
        display: flex;
        align-items: center;
        gap: 12px;
        min-height: 60px;
    }

    .conversation-detail-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .conversation-detail-info {
        flex: 1;
    }

    .conversation-detail-name {
        font-size: 17px;
        font-weight: 600;
        margin-bottom: 2px;
        color: #000000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .conversation-detail-status {
        font-size: 13px;
        color: #8E8E93;
        font-weight: 400;
    }

    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: var(--spacing-base) var(--spacing-xl);
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
        scroll-behavior: smooth;
    }

    .messages-container::-webkit-scrollbar {
        width: 6px;
    }

    .messages-container::-webkit-scrollbar-track {
        background: transparent;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 3px;
    }

    .messages-container::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
    }

    .message {
        display: flex;
        flex-direction: column;
        margin-bottom: var(--spacing-base);
        animation: messageSlideIn 0.3s ease-out;
        clear: both;
    }

    @keyframes messageSlideIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .message.sent {
        align-items: flex-end;
    }

    .message.received {
        align-items: flex-start;
    }

    .message-bubble {
        max-width: 320px;
        min-width: 80px;
        padding: 12px 16px;
        border-radius: 20px;
        word-wrap: break-word;
        word-break: break-word;
        position: relative;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
        line-height: 1.4;
        font-size: 15px;
        margin-bottom: 4px;
    }

    .message.sent .message-bubble {
        background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
        color: var(--white);
        border-bottom-right-radius: 8px;
    }

    .message.received .message-bubble {
        background-color: #F0F0F0;
        color: #000000;
        border-bottom-left-radius: 8px;
        border: none;
    }

    .message-time {
        font-size: 11px;
        opacity: 0.6;
        margin: 2px 8px 0;
        font-weight: 400;
    }

    .message.sent .message-time {
        color: var(--text-light);
        text-align: right;
    }

    .message.received .message-time {
        color: var(--text-light);
        text-align: left;
    }

    /* Consecutive message grouping */
    .message + .message.sent .message-bubble {
        margin-top: -8px;
    }

    .message + .message.received .message-bubble {
        margin-top: -8px;
    }

    /* Different user messages have normal spacing */
    .message.sent + .message.received,
    .message.received + .message.sent {
        margin-top: var(--spacing-base);
    }

    .message-input-container {
        padding: 16px 20px 20px;
        border-top: 1px solid #E5E5EA;
        background-color: var(--white);
        min-height: 60px;
        display: flex;
        align-items: center;
    }

    .message-form {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        width: 100%;
        background-color: #F2F2F7;
        border-radius: 24px;
        padding: 8px 12px;
        border: 1px solid #E5E5EA;
        transition: all 0.2s ease;
    }

    .message-form:focus-within {
        border-color: #007AFF;
        background-color: var(--white);
        box-shadow: 0 0 0 1px #007AFF;
    }

    .message-input {
        flex: 1;
        padding: 8px 12px;
        border: none;
        background: transparent;
        border-radius: 18px;
        font-size: 16px;
        resize: none;
        min-height: 20px;
        max-height: 100px;
        line-height: 1.4;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .message-input:focus {
        outline: none;
    }

    .message-input::placeholder {
        color: #8E8E93;
        font-size: 16px;
    }

    .send-button {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #007AFF;
        color: var(--white);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
        margin-bottom: 2px;
    }

    .send-button:hover {
        background: #0056CC;
        transform: scale(1.05);
    }

    .send-button:active {
        transform: scale(0.95);
    }

    .send-button:disabled {
        background: #C7C7CC;
        cursor: not-allowed;
        transform: none;
    }

    .send-button i {
        font-size: 14px;
        margin-left: 1px;
    }

    /* Pet Inquiry Styles */
    .pet-inquiry-card {
        background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
        color: var(--white);
        border-radius: 20px;
        padding: 16px;
        margin-bottom: 4px;
        max-width: 320px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
        border-bottom-right-radius: 8px;
        font-size: 15px;
        line-height: 1.4;
    }

    .pet-inquiry-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 8px;
        font-weight: 600;
        font-size: 15px;
    }

    .pet-inquiry-price {
        background-color: rgba(255, 255, 255, 0.2);
        padding: 4px 8px;
        border-radius: 8px;
        font-weight: 600;
        margin-left: auto;
        font-size: 13px;
    }

    .pet-inquiry-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
    }

    .inquiry-btn {
        flex: 1;
        padding: 8px 12px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 13px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .inquiry-btn.accept {
        background-color: #34C759;
        color: var(--white);
    }

    .inquiry-btn.decline {
        background-color: #FF3B30;
        color: var(--white);
    }

    .inquiry-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .inquiry-btn:active {
        transform: translateY(0);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .conversation-list {
            width: 100%;
            min-width: unset;
        }

        .messaging-container {
            height: calc(100vh - 100px);
        }

        .message-bubble {
            max-width: 85%;
        }

        .message-input-container {
            padding: var(--spacing-sm) var(--spacing-base);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="messaging-container">
        <div class="conversation-list">
            <div class="conversation-list-header">
                <h2 class="conversation-list-title">Messages</h2>
                <button type="button" class="new-message-button" id="new-message-btn">
                    <i class="fas fa-edit"></i>
                </button>
            </div>

            <div class="search-conversations">
                <input type="text" placeholder="Search conversations..." class="search-input" id="search-conversations">
            </div>

            <div class="conversation-items" id="conversation-items">
                {% for conversation in conversations %}
                    <div class="conversation-item" data-conversation-id="{{ conversation.id }}">
                        {% if conversation.other_user %}
                            {% if conversation.other_user.profile_picture %}
                                <img src="{{ conversation.other_user.profile_picture.url }}" alt="{{ conversation.other_user.username }}" class="conversation-avatar">
                            {% else %}
                                <img src="/static/img/default-avatar.svg" alt="{{ conversation.other_user.username }}" class="conversation-avatar">
                            {% endif %}
                        {% else %}
                            <img src="/static/img/default-avatar.svg" alt="User" class="conversation-avatar">
                        {% endif %}

                        <div class="conversation-info">
                            <div class="conversation-header">
                                <div class="conversation-name">
                                    {% if conversation.other_user %}
                                        {{ conversation.other_user.username }}
                                    {% else %}
                                        Unknown User
                                    {% endif %}
                                </div>
                                <div class="conversation-time">
                                    {% if conversation.last_message %}
                                        {{ conversation.last_message.created_at|date:"g:i A" }}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="conversation-preview">
                                {% if conversation.last_message %}
                                    {% if conversation.last_message.sender == user %}
                                        <span>You:</span>
                                    {% endif %}
                                    {{ conversation.last_message.content|truncatechars:30 }}
                                {% else %}
                                    No messages yet
                                {% endif %}
                            </div>
                        </div>

                        <div class="conversation-status">
                            {% if conversation.unread_count > 0 and conversation.last_message and conversation.last_message.sender != user %}
                                <div class="unread-badge">{{ conversation.unread_count }}</div>
                            {% endif %}
                        </div>
                    </div>
                {% empty %}
                    <div class="empty-state">
                        <p>No conversations yet.</p>
                        <button type="button" class="btn btn-primary" id="empty-new-message-btn">Start a Conversation</button>
                    </div>
                {% endfor %}
            </div>
        </div>

        <div class="conversation-content" id="conversation-content">
            <div class="empty-conversation" id="empty-conversation">
                <div class="empty-conversation-icon">
                    <i class="far fa-comments"></i>
                </div>
                <h3 class="empty-conversation-message">Select a conversation to start messaging</h3>
                <p>Or start a new conversation by clicking the new message button.</p>
            </div>

            <!-- Conversation Detail Container (hidden by default) -->
            <div class="conversation-detail" id="conversation-detail" style="display: none;">
                <div class="conversation-detail-header" id="conversation-header">
                    <!-- Header content will be loaded via AJAX -->
                </div>

                <div class="messages-container" id="messages-container">
                    <!-- Messages will be loaded via AJAX -->
                </div>

                <div class="message-input-container" id="message-input-container">
                    <!-- Message input will be loaded via AJAX -->
                </div>
            </div>
        </div>
    </div>

    <!-- New Message Modal -->
    <div class="new-message-modal" id="new-message-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">New Message</h3>
                <button type="button" class="modal-close" id="modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <form id="new-conversation-form">
                    <div class="form-group">
                        <label for="recipient" class="form-label">To:</label>
                        <input type="text" id="recipient" name="recipient" class="form-control" placeholder="Enter username" required>
                        <div id="recipient-suggestions" class="dropdown-menu"></div>
                    </div>

                    <div class="form-group">
                        <label for="message" class="form-label">Message:</label>
                        <textarea id="message" name="message" rows="4" class="form-control" placeholder="Type your message here..." required></textarea>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="modal-cancel-btn">Cancel</button>
                <button type="button" class="btn btn-primary" id="send-message-btn">Send Message</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let currentConversationId = null;
        let messagePollingInterval = null;

        // Initialize conversation click handlers
        initializeConversationHandlers();

        // New Message Modal
        const newMessageBtn = document.getElementById('new-message-btn');
        const emptyNewMessageBtn = document.getElementById('empty-new-message-btn');
        const newMessageModal = document.getElementById('new-message-modal');
        const modalCloseBtn = document.getElementById('modal-close-btn');
        const modalCancelBtn = document.getElementById('modal-cancel-btn');
        const sendMessageBtn = document.getElementById('send-message-btn');

        function openModal() {
            newMessageModal.classList.add('show');
        }

        function closeModal() {
            newMessageModal.classList.remove('show');
            document.getElementById('new-conversation-form').reset();
        }

        if (newMessageBtn) {
            newMessageBtn.addEventListener('click', openModal);
        }

        if (emptyNewMessageBtn) {
            emptyNewMessageBtn.addEventListener('click', openModal);
        }

        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', closeModal);
        }

        if (modalCancelBtn) {
            modalCancelBtn.addEventListener('click', closeModal);
        }

        // Close modal when clicking outside
        newMessageModal.addEventListener('click', function(e) {
            if (e.target === newMessageModal) {
                closeModal();
            }
        });

        // Send new message
        if (sendMessageBtn) {
            sendMessageBtn.addEventListener('click', function() {
                const recipient = document.getElementById('recipient').value;
                const message = document.getElementById('message').value;

                if (!recipient || !message) {
                    return;
                }

                // Send message via AJAX
                fetch(`{% url 'start-conversation' username='placeholder' %}`.replace('placeholder', recipient), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect to the new conversation
                        window.location.href = data.redirect_url;
                    } else {
                        // Show error message
                        console.error('Error:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }

        // Initialize conversation click handlers
        function initializeConversationHandlers() {
            const conversationItems = document.querySelectorAll('.conversation-item');

            conversationItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const conversationId = this.getAttribute('data-conversation-id');
                    if (conversationId) {
                        loadConversation(conversationId);
                    }
                });
            });
        }

        // Search conversations
        const searchInput = document.getElementById('search-conversations');
        const conversationItems = document.querySelectorAll('.conversation-item');

        if (searchInput && conversationItems.length) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                conversationItems.forEach(item => {
                    const name = item.querySelector('.conversation-name').textContent.toLowerCase();
                    const preview = item.querySelector('.conversation-preview').textContent.toLowerCase();

                    if (name.includes(searchTerm) || preview.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }

        // Load conversation
        function loadConversation(conversationId) {
            // Clear any existing polling
            if (messagePollingInterval) {
                clearInterval(messagePollingInterval);
            }

            // Update active conversation
            currentConversationId = conversationId;

            // Update UI to show active conversation
            updateActiveConversation(conversationId);

            // Show conversation detail and hide empty state
            document.getElementById('empty-conversation').style.display = 'none';
            document.getElementById('conversation-detail').style.display = 'flex';

            // Load conversation data
            Promise.all([
                loadConversationHeader(conversationId),
                loadMessages(conversationId),
                loadMessageInput(conversationId)
            ]).then(() => {
                // Scroll to bottom of messages
                scrollToBottom();

                // Setup message form
                setupMessageForm();

                // Start polling for new messages
                startMessagePolling();
            }).catch(error => {
                console.error('Error loading conversation:', error);
                showErrorMessage('Failed to load conversation');
            });
        }

        // Load conversation header
        function loadConversationHeader(conversationId) {
            return fetch(`/messages/conversation-header/${conversationId}/`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('conversation-header').innerHTML = `
                        <img src="${data.user.profile_picture || '/static/img/default-avatar.svg'}"
                             alt="${data.user.username}" class="conversation-detail-avatar">
                        <div class="conversation-detail-info">
                            <div class="conversation-detail-name">${data.user.username}</div>
                            <div class="conversation-detail-status">
                                ${data.user.is_online ? '<span class="text-success">Online</span>' :
                                  `Last seen ${data.user.last_seen} ago`}
                            </div>
                        </div>
                    `;
                }
            });
        }

        // Load messages
        function loadMessages(conversationId) {
            return fetch(`/messages/conversation-messages/${conversationId}/`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const messagesContainer = document.getElementById('messages-container');
                    messagesContainer.innerHTML = '';

                    data.messages.forEach((message, index) => {
                        const messageElement = document.createElement('div');
                        messageElement.className = `message ${message.is_self ? 'sent' : 'received'}`;
                        messageElement.setAttribute('data-message-id', message.id);

                        let messageContent = '';
                        if (message.message_type === 'pet_inquiry') {
                            messageContent = createPetInquiryMessage(message);
                        } else {
                            messageContent = `
                                <div class="message-bubble">
                                    ${message.content}
                                    <div class="message-time">${message.created_at}</div>
                                </div>
                            `;
                        }

                        messageElement.innerHTML = messageContent;
                        messagesContainer.appendChild(messageElement);
                    });

                    // Scroll to bottom after loading all messages (without animation for initial load)
                    setTimeout(() => scrollToBottom(false), 50);
                }
            });
        }

        // Load message input
        function loadMessageInput(conversationId) {
            return new Promise((resolve) => {
                document.getElementById('message-input-container').innerHTML = `
                    <form class="message-form" id="message-form" action="/messages/conversation/${conversationId}/" method="post">
                        <input type="hidden" name="csrfmiddlewaretoken" value="${getCookie('csrftoken')}">
                        <textarea class="message-input" id="message-input" name="content"
                                  placeholder="Type a message..." rows="1"></textarea>
                        <button type="submit" class="send-button" id="send-button">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                `;
                resolve();
            });
        }

        // Setup message form
        function setupMessageForm() {
            const messageForm = document.getElementById('message-form');
            const messageInput = document.getElementById('message-input');

            if (messageForm) {
                messageForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const content = messageInput.value.trim();
                    if (!content) return;

                    const formData = new FormData(this);

                    fetch(this.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Add new message to the conversation
                            appendMessage(data.message);

                            // Clear input
                            messageInput.value = '';

                            // Scroll to bottom
                            scrollToBottom();

                            // Update conversation list
                            updateConversationPreview(currentConversationId, data.message);
                        } else {
                            showErrorMessage('Failed to send message');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showErrorMessage('Failed to send message');
                    });
                });

                // Auto-resize textarea
                if (messageInput) {
                    messageInput.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                    });

                    // Send on Enter (but not Shift+Enter)
                    messageInput.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            messageForm.dispatchEvent(new Event('submit'));
                        }
                    });
                }
            }
        }

        // Append message to conversation
        function appendMessage(message) {
            const messagesContainer = document.getElementById('messages-container');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${message.is_self ? 'sent' : 'received'}`;
            messageElement.setAttribute('data-message-id', message.id);

            let messageContent = '';
            if (message.message_type === 'pet_inquiry') {
                messageContent = createPetInquiryMessage(message);
            } else {
                messageContent = `
                    <div class="message-bubble">
                        ${message.content}
                        <div class="message-time">${message.created_at}</div>
                    </div>
                `;
            }

            messageElement.innerHTML = messageContent;
            messagesContainer.appendChild(messageElement);

            // Auto-scroll to bottom after adding message
            setTimeout(() => scrollToBottom(true), 100);
        }

        // Create pet inquiry message
        function createPetInquiryMessage(message) {
            return `
                <div class="pet-inquiry-card">
                    <div class="pet-inquiry-header">
                        <i class="fas fa-paw"></i>
                        <span>Pet Inquiry</span>
                        ${message.pet_price ? `<div class="pet-inquiry-price">$${message.pet_price}</div>` : ''}
                    </div>
                    <div class="message-content">${message.content}</div>
                    ${message.show_actions ? `
                        <div class="pet-inquiry-actions">
                            <button class="inquiry-btn accept" onclick="respondToInquiry(${message.id}, 'interested')">
                                <i class="fas fa-check"></i> Interested
                            </button>
                            <button class="inquiry-btn decline" onclick="respondToInquiry(${message.id}, 'declined')">
                                <i class="fas fa-times"></i> Decline
                            </button>
                        </div>
                    ` : ''}
                    <div class="message-time">${message.created_at}</div>
                </div>
            `;
        }

        // Update active conversation in sidebar
        function updateActiveConversation(conversationId) {
            const conversationItems = document.querySelectorAll('.conversation-item');
            conversationItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('data-conversation-id') === conversationId) {
                    item.classList.add('active');
                }
            });
        }

        // Update conversation preview in sidebar
        function updateConversationPreview(conversationId, message) {
            const conversationItem = document.querySelector(`[data-conversation-id="${conversationId}"]`);
            if (conversationItem) {
                const previewElement = conversationItem.querySelector('.conversation-preview');
                const timeElement = conversationItem.querySelector('.conversation-time');

                if (previewElement) {
                    previewElement.innerHTML = `<span>You:</span> ${message.content.substring(0, 30)}${message.content.length > 30 ? '...' : ''}`;
                }

                if (timeElement) {
                    timeElement.textContent = 'now';
                }

                // Move conversation to top
                const conversationList = document.getElementById('conversation-items');
                conversationList.insertBefore(conversationItem, conversationList.firstChild);
            }
        }

        // Scroll to bottom of messages
        function scrollToBottom(smooth = true) {
            const messagesContainer = document.getElementById('messages-container');
            if (messagesContainer) {
                if (smooth) {
                    messagesContainer.scrollTo({
                        top: messagesContainer.scrollHeight,
                        behavior: 'smooth'
                    });
                } else {
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                }
            }
        }

        // Start polling for new messages
        function startMessagePolling() {
            if (messagePollingInterval) {
                clearInterval(messagePollingInterval);
            }

            messagePollingInterval = setInterval(() => {
                if (currentConversationId) {
                    checkForNewMessages();
                }
            }, 3000); // Poll every 3 seconds
        }

        // Check for new messages
        function checkForNewMessages() {
            const lastMessageElement = document.querySelector('#messages-container .message:last-child');
            const lastMessageId = lastMessageElement ? lastMessageElement.getAttribute('data-message-id') : 0;

            fetch(`/messages/conversation-messages/${currentConversationId}/?last_id=${lastMessageId}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.messages.length > 0) {
                    data.messages.forEach(message => {
                        appendMessage(message);
                    });
                    scrollToBottom();
                }
            })
            .catch(error => {
                console.error('Error checking for new messages:', error);
            });
        }

        // Show error message
        function showErrorMessage(message) {
            // Create a simple error notification
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: var(--danger);
                color: var(--white);
                padding: var(--spacing-base);
                border-radius: var(--radius-base);
                z-index: 1000;
                box-shadow: var(--shadow-lg);
            `;
            errorDiv.textContent = message;
            document.body.appendChild(errorDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }

        // Respond to pet inquiry
        function respondToInquiry(messageId, response) {
            fetch(`/messages/respond-inquiry/${messageId}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `response=${response}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Reload messages to show the response
                    loadMessages(currentConversationId);
                } else {
                    showErrorMessage('Failed to respond to inquiry');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Failed to respond to inquiry');
            });
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
