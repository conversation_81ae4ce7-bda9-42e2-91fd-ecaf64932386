{% extends 'base.html' %}

{% block title %}Notifications | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .notifications-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }

    .notifications-header {
        margin-bottom: var(--spacing-xl);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .notifications-title {
        font-size: var(--font-2xl);
    }

    .notifications-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    .notification-item {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        gap: var(--spacing-base);
        transition: var(--transition-base);
    }

    .notification-item:last-child {
        border-bottom: none;
    }

    .notification-item:hover {
        background-color: var(--gray-50);
    }

    .notification-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .notification-content {
        flex: 1;
    }

    .notification-message {
        margin-bottom: var(--spacing-xs);
    }

    .notification-message a {
        font-weight: var(--fw-medium);
        color: var(--text);
        text-decoration: none;
    }

    .notification-message a:hover {
        color: var(--primary);
    }

    .notification-time {
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        background-color: var(--gray-100);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text);
    }

    .notification-icon.message {
        background-color: rgba(var(--info-rgb), 0.1);
        color: var(--info);
    }

    .notification-icon.pet_inquiry {
        background-color: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
    }

    .notification-icon.pet_inquiry_response {
        background-color: rgba(var(--success-rgb), 0.1);
        color: var(--success);
    }

    .notification-unread {
        width: 10px;
        height: 10px;
        border-radius: var(--radius-full);
        background-color: var(--primary);
    }

    .empty-state {
        padding: var(--spacing-2xl);
        text-align: center;
    }

    .empty-state p {
        margin-bottom: var(--spacing-base);
        color: var(--text-light);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="notifications-container">
        <div class="notifications-header">
            <h1 class="notifications-title">Notifications</h1>

            <a href="{% url 'inbox' %}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Messages
            </a>
        </div>

        <div class="notifications-card">
            {% for notification in notifications %}
                <div class="notification-item">
                    <img src="{{ notification.sender.profile_picture.url }}" alt="{{ notification.sender.username }}" class="notification-avatar">

                    <div class="notification-content">
                        <div class="notification-message">
                            {% if notification.notification_type == 'message' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> sent you a <a href="{% url 'conversation-detail' pk=notification.conversation.pk %}">message</a>.
                            {% elif notification.notification_type == 'pet_inquiry' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> is interested in your pet <a href="{% url 'pet-detail' pk=notification.pet.pk %}">{{ notification.pet.name }}</a>.
                            {% elif notification.notification_type == 'pet_inquiry_response' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> responded to your pet inquiry for <a href="{% url 'pet-detail' pk=notification.pet.pk %}">{{ notification.pet.name }}</a>.
                            {% else %}
                                {{ notification.message }}
                            {% endif %}
                        </div>
                        <div class="notification-time">{{ notification.created_at|timesince }} ago</div>
                    </div>

                    <div class="notification-icon {{ notification.notification_type }}">
                        {% if notification.notification_type == 'message' %}
                            <i class="fas fa-envelope"></i>
                        {% elif notification.notification_type == 'pet_inquiry' %}
                            <i class="fas fa-paw"></i>
                        {% elif notification.notification_type == 'pet_inquiry_response' %}
                            <i class="fas fa-reply"></i>
                        {% endif %}
                    </div>

                    {% if not notification.is_read %}
                        <div class="notification-unread"></div>
                    {% endif %}
                </div>
            {% empty %}
                <div class="empty-state">
                    <p>You don't have any notifications yet.</p>
                    <p>Start messaging with other users to receive notifications.</p>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
