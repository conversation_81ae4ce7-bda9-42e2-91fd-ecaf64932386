from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.http import JsonResponse
from .models import Conversation, Message, Notification
from .forms import MessageForm
from users.models import User
from pets.models import Pet


@login_required
def inbox(request):
    """View for displaying user's conversations"""
    conversations = Conversation.objects.filter(participants=request.user)

    # Count unread messages for each conversation
    for conversation in conversations:
        conversation.unread_count = conversation.messages.filter(
            sender__in=conversation.participants.exclude(id=request.user.id),
            is_read=False
        ).count()

    return render(request, 'messaging/inbox.html', {'conversations': conversations})


@login_required
def conversation_detail(request, pk):
    """View for displaying a conversation and sending messages"""
    conversation = get_object_or_404(Conversation, pk=pk, participants=request.user)

    # Get all conversations for the sidebar
    conversations = Conversation.objects.filter(participants=request.user)

    # Count unread messages for each conversation
    for conv in conversations:
        conv.unread_count = conv.messages.filter(
            sender__in=conv.participants.exclude(id=request.user.id),
            is_read=False
        ).count()
        # Add other_user attribute for template compatibility
        conv.other_user = conv.get_other_participant(request.user)
        # Add last_message for template compatibility
        conv.last_message = conv.get_last_message()

    # Mark all messages as read
    unread_messages = conversation.messages.filter(
        sender__in=conversation.participants.exclude(id=request.user.id),
        is_read=False
    )
    for msg in unread_messages:
        msg.mark_as_read()

    if request.method == 'POST':
        form = MessageForm(request.POST, request.FILES)
        if form.is_valid():
            message = form.save(commit=False)
            message.conversation = conversation
            message.sender = request.user
            message.save()

            # Update conversation timestamp
            conversation.save()  # This will update the updated_at field

            # Create notification for the other participant
            other_user = conversation.get_other_participant(request.user)
            if other_user:
                Notification.objects.create(
                    recipient=other_user,
                    sender=request.user,
                    notification_type='message',
                    conversation=conversation,
                    message=f"{request.user.username} sent you a message."
                )

            # Handle AJAX requests
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'status': 'success',
                    'message': {
                        'id': message.id,
                        'content': message.content,
                        'sender': message.sender.username,
                        'created_at': message.created_at.strftime('%b %d, %Y, %I:%M %p'),
                        'image_url': message.image.url if message.image else None
                    }
                })

            return redirect('conversation-detail', pk=pk)
    else:
        form = MessageForm()

    return render(request, 'messaging/conversation_detail.html', {
        'conversation': conversation,
        'conversations': conversations,
        'messages': conversation.messages.all(),
        'form': form,
        'other_user': conversation.get_other_participant(request.user)
    })


@login_required
def start_conversation(request, username):
    """View for starting a new conversation with a user"""
    other_user = get_object_or_404(User, username=username)

    # Check if conversation already exists
    conversations = Conversation.objects.filter(participants=request.user).filter(participants=other_user)

    if conversations.exists():
        # Conversation already exists, redirect to it
        return redirect('conversation-detail', pk=conversations.first().id)

    # Create new conversation
    conversation = Conversation.objects.create()
    conversation.participants.add(request.user, other_user)

    return redirect('conversation-detail', pk=conversation.id)


@login_required
def start_pet_inquiry(request, pet_id):
    """View for starting a pet inquiry conversation"""
    pet = get_object_or_404(Pet, id=pet_id)

    # Prevent owner from inquiring about their own pet
    if request.user == pet.owner:
        return JsonResponse({'error': 'You cannot inquire about your own pet'}, status=400)

    if request.method == 'POST':
        # Get or create conversation between users
        conversations = Conversation.objects.filter(participants=request.user).filter(participants=pet.owner)

        if conversations.exists():
            conversation = conversations.first()
        else:
            conversation = Conversation.objects.create()
            conversation.participants.add(request.user, pet.owner)

        # Generate auto message based on pet details
        if pet.is_for_adoption and pet.adoption_price:
            auto_message = f"Hi! I'm interested in adopting {pet.name} ({pet.breed.name if pet.breed else pet.category.name}). I saw they're available for ${pet.adoption_price}. Could you please let me know if {pet.name} is still available and provide more details about the adoption process?"
        elif pet.is_for_adoption:
            auto_message = f"Hi! I'm interested in adopting {pet.name} ({pet.breed.name if pet.breed else pet.category.name}). Could you please let me know if {pet.name} is still available and provide more details about the adoption process?"
        else:
            auto_message = f"Hi! I'm interested in your pet {pet.name} ({pet.breed.name if pet.breed else pet.category.name}). Could you please provide more information about {pet.name}?"

        # Create the inquiry message
        message = Message.objects.create(
            conversation=conversation,
            sender=request.user,
            content=auto_message,
            message_type='pet_inquiry',
            pet=pet,
            inquiry_status='pending'
        )

        # Create notification for pet owner
        Notification.objects.create(
            recipient=pet.owner,
            sender=request.user,
            notification_type='pet_inquiry',
            pet=pet,
            conversation=conversation,
            message=f"{request.user.username} is interested in {pet.name}"
        )

        return JsonResponse({
            'success': True,
            'conversation_id': conversation.id,
            'message': 'Pet inquiry sent successfully!'
        })

    return JsonResponse({'error': 'Invalid request method'}, status=405)


@login_required
def respond_to_pet_inquiry(request, message_id):
    """View for pet owners to respond to inquiries"""
    inquiry_message = get_object_or_404(Message, id=message_id, message_type='pet_inquiry')

    # Only pet owner can respond
    if request.user != inquiry_message.pet.owner:
        return JsonResponse({'error': 'You are not authorized to respond to this inquiry'}, status=403)

    if request.method == 'POST':
        response_type = request.POST.get('response_type')  # 'interested', 'not_available', 'declined'
        custom_message = request.POST.get('custom_message', '')

        # Update inquiry status
        inquiry_message.inquiry_status = response_type
        inquiry_message.save()

        # Generate response message
        pet_name = inquiry_message.pet.name
        if response_type == 'interested':
            auto_response = f"Great! I'm happy to hear you're interested in {pet_name}. {pet_name} is still available. " + custom_message
        elif response_type == 'not_available':
            auto_response = f"Thank you for your interest in {pet_name}. Unfortunately, {pet_name} is no longer available. " + custom_message
        elif response_type == 'declined':
            auto_response = f"Thank you for your interest in {pet_name}. After consideration, I've decided not to proceed with this inquiry. " + custom_message
        else:
            auto_response = custom_message

        # Create response message
        response_message = Message.objects.create(
            conversation=inquiry_message.conversation,
            sender=request.user,
            content=auto_response,
            message_type='pet_response',
            pet=inquiry_message.pet,
            inquiry_status=response_type
        )

        # Create notification for inquirer
        inquirer = inquiry_message.sender
        Notification.objects.create(
            recipient=inquirer,
            sender=request.user,
            notification_type='pet_inquiry_response',
            pet=inquiry_message.pet,
            conversation=inquiry_message.conversation,
            message=f"{request.user.username} responded to your inquiry about {pet_name}"
        )

        return JsonResponse({
            'success': True,
            'message': 'Response sent successfully!',
            'response_type': response_type
        })

    return JsonResponse({'error': 'Invalid request method'}, status=405)


@login_required
def load_messages(request, pk):
    """AJAX view for loading messages in a conversation"""
    conversation = get_object_or_404(Conversation, pk=pk, participants=request.user)

    # Get messages after a certain ID if provided
    last_id = request.GET.get('last_id')
    if last_id:
        messages_list = conversation.messages.filter(id__gt=last_id)
    else:
        messages_list = conversation.messages.all()

    # Mark messages as read
    for msg in messages_list:
        if msg.sender != request.user and not msg.is_read:
            msg.mark_as_read()

    # Format messages for JSON response
    messages_data = []
    for msg in messages_list:
        messages_data.append({
            'id': msg.id,
            'content': msg.content,
            'sender': msg.sender.username,
            'is_self': msg.sender == request.user,
            'created_at': msg.created_at.strftime('%b %d, %Y, %I:%M %p'),
            'image_url': msg.image.url if msg.image else None
        })

    return JsonResponse({'messages': messages_data})


@login_required
def notifications(request):
    """View for displaying user notifications"""
    notifications = Notification.objects.filter(recipient=request.user).order_by('-created_at')

    # Handle AJAX requests for marking notifications as read
    if request.method == 'POST' and request.headers.get('x-requested-with') == 'XMLHttpRequest':
        notification_id = request.POST.get('notification_id')
        mark_all = request.POST.get('mark_all')

        if mark_all == 'true':
            # Mark all unread notifications as read
            unread_notifications = Notification.objects.filter(
                recipient=request.user,
                is_read=False
            )
            unread_notifications.update(is_read=True)
            return JsonResponse({
                'status': 'success',
                'marked_count': unread_notifications.count()
            })

        elif notification_id:
            try:
                notification = Notification.objects.get(id=notification_id, recipient=request.user)
                if not notification.is_read:
                    notification.is_read = True
                    notification.save()
                return JsonResponse({'status': 'success'})
            except Notification.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': 'Notification not found'})

    # Don't automatically mark notifications as read - let users interact with them

    return render(request, 'messaging/notifications.html', {'notifications': notifications})


@login_required
def notification_count(request):
    """AJAX endpoint to get current unread notification count"""
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        count = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).count()
        return JsonResponse({
            'status': 'success',
            'count': count
        })
    return JsonResponse({'status': 'error', 'message': 'Invalid request'})
