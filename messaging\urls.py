from django.urls import path
from . import views

urlpatterns = [
    path('', views.inbox, name='inbox'),
    path('conversation/<int:pk>/', views.conversation_detail, name='conversation-detail'),
    path('start/<str:username>/', views.start_conversation, name='start-conversation'),
    path('load-messages/<int:pk>/', views.load_messages, name='load-messages'),
    path('pet-inquiry/<int:pet_id>/', views.start_pet_inquiry, name='start-pet-inquiry'),
    path('respond-inquiry/<int:message_id>/', views.respond_to_pet_inquiry, name='respond-pet-inquiry'),
    path('notifications/', views.notifications, name='notifications'),
    path('notification-count/', views.notification_count, name='notification-count'),
]
