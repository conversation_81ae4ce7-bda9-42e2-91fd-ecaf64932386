{% extends 'base.html' %}

{% block title %}Add Milestone for {{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .milestone-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .milestone-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .milestone-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
    
    .form-actions {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-2xl);
    }
    
    .file-upload-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--radius-md);
        padding: var(--spacing-xl);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: var(--spacing-lg);
    }
    
    .file-upload-container:hover {
        border-color: var(--primary);
        background-color: var(--gray-50);
    }
    
    .file-upload-icon {
        font-size: 2rem;
        color: var(--gray-400);
        margin-bottom: var(--spacing-md);
    }
    
    .file-upload-text {
        font-weight: 500;
        margin-bottom: var(--spacing-xs);
    }
    
    .file-upload-info {
        font-size: var(--font-sm);
        color: var(--gray-500);
    }
    
    .milestone-examples {
        background-color: var(--gray-50);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .milestone-examples h4 {
        margin-bottom: var(--spacing-md);
        color: var(--gray-700);
    }
    
    .milestone-examples ul {
        margin: 0;
        padding-left: var(--spacing-lg);
    }
    
    .milestone-examples li {
        margin-bottom: var(--spacing-xs);
        color: var(--gray-600);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="milestone-form-container">
        <div class="milestone-form-header">
            <h1>Add Milestone for {{ pet.name }}</h1>
            <p>Record important achievements and developmental milestones</p>
        </div>
        
        <div class="milestone-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Milestone Information</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.milestone_type.id_for_label }}" class="form-label">Milestone Type</label>
                            {{ form.milestone_type }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.date_achieved.id_for_label }}" class="form-label">Date Achieved</label>
                            {{ form.date_achieved }}
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.title.id_for_label }}" class="form-label">Title</label>
                        {{ form.title }}
                        <small class="form-text">Brief title for this milestone (e.g., "First Walk", "Learned to Sit")</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        <small class="form-text">Detailed description of the milestone achievement</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.age_at_milestone.id_for_label }}" class="form-label">Age at Milestone (Optional)</label>
                        {{ form.age_at_milestone }}
                        <small class="form-text">e.g., "3 months", "1 year 2 months"</small>
                    </div>
                </div>
                
                <div class="milestone-examples">
                    <h4>Milestone Examples by Type:</h4>
                    <ul>
                        <li><strong>Physical:</strong> First steps, opening eyes, losing baby teeth</li>
                        <li><strong>Behavioral:</strong> House trained, sleeping through the night</li>
                        <li><strong>Training:</strong> Learned to sit, come when called, walk on leash</li>
                        <li><strong>Health:</strong> First vaccination, spay/neuter, clean bill of health</li>
                        <li><strong>Social:</strong> First playdate, meeting other pets, socialization</li>
                    </ul>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Media (Optional)</h2>
                    
                    <div class="file-upload-container" id="photo-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-image"></i>
                        </div>
                        <div class="file-upload-text">Click to upload a photo</div>
                        <div class="file-upload-info">JPG, PNG, or GIF • Max 10MB</div>
                        <div style="display:none;">{{ form.photo }}</div>
                    </div>
                    
                    <div class="file-upload-container" id="video-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-play-circle"></i>
                        </div>
                        <div class="file-upload-text">Click to upload a video</div>
                        <div class="file-upload-info">MP4, MOV, or AVI • Max 50MB</div>
                        <div style="display:none;">{{ form.video }}</div>
                    </div>
                    
                    <div id="file-name-display" class="form-text"></div>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Add Milestone</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set today's date as default
        const dateInput = document.querySelector('input[name="date_achieved"]');
        if (dateInput && !dateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }
        
        // File upload handling for photo
        const photoUploadContainer = document.getElementById('photo-upload-container');
        const photoInput = document.querySelector('input[name="photo"]');
        
        // File upload handling for video
        const videoUploadContainer = document.getElementById('video-upload-container');
        const videoInput = document.querySelector('input[name="video"]');
        
        const fileNameDisplay = document.getElementById('file-name-display');
        
        if (photoUploadContainer && photoInput) {
            photoUploadContainer.addEventListener('click', function() {
                photoInput.click();
            });
            
            photoInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const fileName = this.files[0].name;
                    fileNameDisplay.textContent = `Selected photo: ${fileName}`;
                    photoUploadContainer.style.borderColor = 'var(--primary)';
                    videoUploadContainer.style.borderColor = 'var(--gray-300)';
                    videoInput.value = '';
                } else {
                    fileNameDisplay.textContent = '';
                    photoUploadContainer.style.borderColor = 'var(--gray-300)';
                }
            });
        }
        
        if (videoUploadContainer && videoInput) {
            videoUploadContainer.addEventListener('click', function() {
                videoInput.click();
            });
            
            videoInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const fileName = this.files[0].name;
                    fileNameDisplay.textContent = `Selected video: ${fileName}`;
                    videoUploadContainer.style.borderColor = 'var(--primary)';
                    photoUploadContainer.style.borderColor = 'var(--gray-300)';
                    photoInput.value = '';
                } else {
                    fileNameDisplay.textContent = '';
                    videoUploadContainer.style.borderColor = 'var(--gray-300)';
                }
            });
        }
    });
</script>
{% endblock %>
